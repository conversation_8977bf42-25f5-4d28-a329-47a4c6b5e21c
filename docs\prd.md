### **它宇宙 - 后台管理系统开发需求文档 (PRD)**

- **版本：** 1.0
- **日期：** 2023 年 10 月 27 日
- **目标读者：** 全栈开发工程师

---

#### **1. 项目概述**

**1.1 项目目标**
为“AI 玩偶”微信小程序开发一个统一、高效的 Web 后台管理系统。该系统旨在服务内部**超级管理员**和外部**渠道代理商**两类用户，通过精细的权限控制，实现功能和数据的安全隔离。

**1.2 核心用户角色**

- **超级管理员 (SuperAdmin):** 公司内部运营人员，拥有系统所有权限，负责系统维护、代理商管理、全局数据监控等。
- **代理商 (Agent):** 外部合作伙伴，权限受限，仅能访问和管理自身业务相关的数据，如查看业绩、管理名下用户和优惠券。

---

#### **2. 技术栈**

- **后端：** Python 3.10+ / FastAPI
- **前端：** Vue 3 (Composition API) / TypeScript
- **UI 组件库：** Element Plus (推荐)
- **状态管理：** Pinia
- **数据库：** MySQL 8.0+

---

#### **3. 核心架构：统一系统 + RBAC**

**关键原则：**

- **单一应用：** 开发一个后端 API 和一个前端 Web 应用，供所有后台角色使用。
- **角色驱动：** 系统通过**基于角色的访问控制 (Role-Based Access Control, RBAC)** 机制，在用户登录后，根据其角色动态渲染菜单、页面和过滤数据。
- **用户分离：** 后台管理用户（管理员/代理商）与小程序 C 端用户在数据库层面完全分离。

---

#### **4. 数据库设计 (MySQL)**

**需创建的新表：**

1.  **`admin_users` (后台用户表)**

    ```sql
    CREATE TABLE `admin_users` (
      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '登录账号',
      `password_hash` VARCHAR(255) NOT NULL COMMENT 'bcrypt加密后的密码',
      `nickname` VARCHAR(50) COMMENT '用户昵称',
      `role_id` INT UNSIGNED NOT NULL COMMENT '外键->roles.id',
      `parent_id` INT UNSIGNED NULL COMMENT '创建者ID (admin_user.id)，用于追踪代理商归属',
      `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 1=启用, 0=禁用',
      `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX `idx_role_id` (`role_id`),
      INDEX `idx_parent_id` (`parent_id`)
    );
    ```

2.  **`roles` (角色表)**

    ```sql
    CREATE TABLE `roles` (
      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `name` VARCHAR(50) NOT NULL UNIQUE COMMENT '角色英文标识，如 SuperAdmin',
      `description` VARCHAR(255) COMMENT '角色描述'
    );
    -- 预置数据
    INSERT INTO `roles` (id, name, description) VALUES (1, 'SuperAdmin', '超级管理员'), (2, 'Agent', '代理商');
    ```

3.  **`coupon_tiers` (优惠券档次表)** - _用于管理固定的优惠券面额_

    ```sql
    CREATE TABLE `coupon_tiers` (
      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `name` VARCHAR(50) NOT NULL COMMENT '档次名称，如 "29.9元套餐"',
      `value` DECIMAL(10, 2) NOT NULL UNIQUE COMMENT '档次面额'
    );
    -- 预置数据
    INSERT INTO `coupon_tiers` (name, value) VALUES ('29.9元套餐', 29.90), ('49.9元套餐', 49.90), ('99.9元套餐', 99.90);
    ```

4.  **`coupon_batches` (优惠券批次表)**

    ```sql
    CREATE TABLE `coupon_batches` (
      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `name` VARCHAR(100) NOT NULL COMMENT '批次名称，如 "2023双十一活动"',
      `tier_id` INT UNSIGNED NOT NULL COMMENT '外键->coupon_tiers.id',
      `total_quantity` INT NOT NULL COMMENT '该批次总发行量',
      `issued_quantity` INT NOT NULL DEFAULT 0 COMMENT '已发放数量',
      `valid_from` DATE NOT NULL COMMENT '有效期开始日期',
      `valid_to` DATE NOT NULL COMMENT '有效期结束日期',
      `created_by` INT UNSIGNED NOT NULL COMMENT '创建者ID->admin_users.id',
      `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      INDEX `idx_tier_id` (`tier_id`),
      INDEX `idx_created_by` (`created_by`)
    );
    ```

5.  **`coupons` (优惠券实例表)**
    ```sql
    CREATE TABLE `coupons` (
      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `code` VARCHAR(50) NOT NULL UNIQUE COMMENT '优惠券码，唯一，用于兑换',
      `batch_id` INT UNSIGNED NOT NULL COMMENT '外键->coupon_batches.id',
      `owner_id` INT UNSIGNED NULL COMMENT '拥有者ID->admin_users.id (代理商ID)',
      `status` VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE' COMMENT '状态: AVAILABLE(可用), USED(已使用), EXPIRED(已过期)',
      `used_at` DATETIME NULL COMMENT '使用时间',
      `used_by_user_id` INT UNSIGNED NULL COMMENT '使用者ID->wechat_users.id',
      `used_in_order_id` VARCHAR(100) NULL COMMENT '用于哪个订单ID',
      `assigned_at` DATETIME NULL COMMENT '分配给代理商的时间',
      INDEX `idx_batch_id` (`batch_id`),
      INDEX `idx_owner_id` (`owner_id`),
      INDEX `idx_status` (`status`)
    );
    ```

**RBAC 权限表 (可选，但推荐):**

- `permissions` (权限表) 和 `role_permissions` (关联表) 可用于更精细的权限控制，初期可暂缓，先在代码中基于角色硬编码，后期重构。

---

#### **5. 系统级功能**

**5.1 认证与授权 (Auth)**

- **登录:** 提供 `/login` 页面，使用账号密码登录。后端验证成功后生成 **JWT** 返回。JWT Payload 须包含 `user_id`, `role` (`SuperAdmin`/`Agent`)。
- **鉴权:** 前端请求需在 Header 中携带 `Authorization: Bearer <JWT>`。后端通过 FastAPI 中间件/依赖项验证 JWT，并解析出用户信息。
- **权限控制:** 后端 API 需根据用户角色进行访问控制。无权访问返回 `403 Forbidden`。
- **数据隔离:** **（核心要求）** 代理商登录后，所有数据查询类 API 必须在 SQL 层面自动添加 `WHERE owner_id = [当前登录代理商ID]` 或类似过滤，确保数据安全。
- **前端路由:** 基于登录后获取的 `role` 动态生成菜单，并使用 Vue Router 导航守卫，防止越权访问 URL。

---

#### **6. 功能模块详述**

##### **通用模块**

- **登录页 (`/login`)**: 统一登录入口。
- **主框架**: 包含顶部导航栏（用户昵称、修改密码、退出登录）和左侧动态菜单栏。
- **个人中心 (`/profile`)**: 提供修改当前用户登录密码的功能。

##### **超级管理员 (SuperAdmin) 视图**

1.  **仪表盘 (Dashboard)**

    - **KPI 卡片:** 总用户数、总代理商数、总销售额、今日新增用户、今日销售额。
    - **图表:** 近 30 天销售额趋势图（折线图）、各代理商业绩贡献占比（饼图）。

2.  **代理商管理 (Agent Management)**

    - **列表页:** 表格展示所有代理商（账号、昵称、状态、创建时间、名下用户数、总销售额）。支持搜索、分页。
    - **创建:** 弹出表单，输入代理商的登录账号、昵称、初始密码。
    - **操作:** 编辑（修改昵称、重置密码）、切换状态（启用/禁用）。

3.  **优惠券管理 (Coupon Management)**

    - **优惠券批次管理 (`/coupons/batches`):**
      - **查看:** 列表展示所有批次（批次名称、优惠券档次、总数量、已发放数、有效期）。
      - **创建:** 弹出表单，填写批次名称，从**下拉框**中选择一个优惠券档次（从`coupon_tiers`表动态获取），输入发行量、有效期。
      - **分配:** 对批次进行操作，选择一个代理商并输入分配数量。后端进行库存校验，成功后在`coupons`表生成记录并更新批次已发放数。
      - **查看实例:** 点击批次可跳转查看该批次下所有优惠券的详细列表。
    - **优惠券实例总览 (`/coupons/instances`):**
      - 查看全站所有优惠券实例，提供强大的筛选功能（按批次、代理商、状态、用户等）。

4.  **用户管理 (C-end User Management)**

    - 查看所有小程序用户列表（微信昵称、手机号、注册时间、来源代理商）。支持搜索。

5.  **系统工具 (System Tools)**
    - **优惠券使用模拟器 (`/tools/coupon-simulator`):**
      - **目的:** 用于测试和演示。
      - **UI:** 提供代理商选择器（可选）、C 端用户选择器（可选）、模拟数量输入框、执行按钮、实时日志输出区。
      - **后端逻辑 (`POST /api/v1/admin/simulator/use-coupons`):** 接收模拟数量和可选的`agent_id`/`user_id`。随机选择符合条件的可用优惠券和用户，**在事务中**将其状态更新为`USED`，并记录使用信息。
    - **操作日志:** (后期规划) 记录后台用户的关键操作。

##### **代理商 (Agent) 视图**

1.  **仪表盘 (Dashboard)**

    - **KPI 卡片:** **我的**总销售额、**我的**用户总数、**我名下**优惠券可用/已用总数。
    - **图表:** 近 30 天**我的**销售额趋势图。

2.  **我的销售 (My Sales)**

    - 查看**自己渠道**下所有用户的消费记录列表（订单号、用户、金额、时间）。

3.  **我的优惠券 (My Coupons)**

    - **概览:** 卡片展示可用、已用、已过期的优惠券数量。
    - **列表:** 查看**分配给自己**的所有优惠券实例（券码、面额、状态、有效期、使用信息）。支持按状态筛选。
    - **操作:** 提供“复制券码”功能。

4.  **我的用户 (My Users)**
    - 查看所有**通过自己渠道**注册的用户列表（昵称、手机号、注册时间）。

---

#### **7. API 设计原则**

- **风格:** 遵循 RESTful 规范。
- **URL:** 使用统一前缀，如 `/api/v1/admin/...`。
- **响应:** 采用统一 JSON 结构，如 `{ "code": 0, "message": "success", "data": { ... } }`。
- **校验:** 后端使用 Pydantic 对所有请求和响应数据进行严格校验和序列化。

---

#### **8. 验收标准**

1.  系统功能完整，符合本文档描述的所有功能点。
2.  角色权限隔离严格，代理商无法通过任何方式访问或操作不属于自己的数据。
3.  优惠券的创建、分配、使用、模拟流程逻辑正确，数据一致。**（核心验收点）**
4.  所有列表页面的搜索、分页功能正常工作。
5.  系统界面友好，操作流畅，无明显 BUG。
