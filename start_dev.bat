@echo off
setlocal enabledelayedexpansion

REM ==========================================
REM Configuration Variables - Keep consistent with setup scripts
REM ==========================================
set ENV_NAME=fastapi-talking-pet-admin
set DEFAULT_HOST=0.0.0.0
set DEFAULT_PORT=8000

echo ========================================
echo Start 它宇宙后台管理系统 (Development Mode)
echo ========================================
echo Environment Name: %ENV_NAME%
echo.

REM Check if conda environment exists
echo [1/3] Checking conda environment...
conda env list | findstr /C:"%ENV_NAME%" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: conda environment '%ENV_NAME%' does not exist
    echo Please run setup_conda_env.bat first to create the environment
    echo.
    pause
    exit /b 1
)
echo OK: Environment '%ENV_NAME%' found

REM Activate environment
echo.
echo [2/3] Activating conda environment...
call conda activate %ENV_NAME%
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate environment
    pause
    exit /b 1
)
echo OK: Environment activated

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py file not found
    echo Please ensure you run this script in the project root directory
    pause
    exit /b 1
)

REM Read configuration from .env file
echo.
echo [3/3] Reading configuration and starting development server...
set HOST=%DEFAULT_HOST%
set PORT=%DEFAULT_PORT%

if exist ".env" (
    echo Reading configuration from .env file...
    
    REM Read HOST from .env
    for /f "tokens=2 delims==" %%a in ('findstr /i "^HOST=" .env 2^>nul') do (
        set HOST=%%a
    )
    
    REM Read PORT from .env
    for /f "tokens=2 delims==" %%a in ('findstr /i "^PORT=" .env 2^>nul') do (
        set PORT=%%a
    )
) else (
    echo WARNING: .env file not found, using default configuration
)

echo.
echo Development server configuration:
echo - Host: %HOST%
echo - Port: %PORT%
echo - Reload: enabled
echo.
echo Application will start at:
echo - Homepage: http://localhost:%PORT%
echo - API Docs: http://localhost:%PORT%/docs
echo - Health Check: http://localhost:%PORT%/health
echo.
echo Press Ctrl+C to stop the development server
echo ========================================
echo.

REM Start development server with uvicorn
python -m uvicorn main:app --host %HOST% --port %PORT% --reload

echo.
echo Development server stopped
pause
