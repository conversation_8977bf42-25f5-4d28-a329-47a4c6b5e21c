#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.core.database import engine, create_tables
from sqlalchemy import text

def test_connection():
    """测试数据库连接"""
    try:
        print(f"正在测试数据库连接...")
        print(f"数据库URL: {settings.database_url}")
        
        # 测试基本连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功!")
            
            # 测试数据库选择
            result = connection.execute(text("SELECT DATABASE()"))
            current_db = result.fetchone()[0]
            print(f"✅ 当前数据库: {current_db}")
            
            # 创建表
            print("正在创建数据表...")
            create_tables()
            print("✅ 数据表创建成功!")
            
            # 检查表是否存在
            result = connection.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"✅ 数据库中的表: {[table[0] for table in tables]}")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_connection()
    if success:
        print("\n🎉 数据库设置完成！现在可以启动FastAPI应用了。")
        print("运行命令: start_dev.bat")
    else:
        print("\n❌ 数据库设置失败，请检查配置。")
    
    input("\n按回车键退出...")
