@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 它宇宙后台管理系统 - 数据库设置
echo ========================================
echo.

REM 检查MySQL是否可用
echo [1/3] 检查MySQL服务...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL命令不可用，请确保MySQL已安装并添加到PATH
    echo 请检查MySQL安装路径是否正确
    pause
    exit /b 1
)
echo OK: MySQL已安装

echo.
echo [2/3] 创建数据库...
echo 请输入MySQL root用户的密码：
mysql -u root -p < setup_database.sql
if %errorlevel% neq 0 (
    echo ERROR: 数据库创建失败
    echo 请检查：
    echo 1. MySQL服务是否运行
    echo 2. root用户密码是否正确
    echo 3. 是否有创建数据库的权限
    pause
    exit /b 1
)
echo OK: 数据库创建成功

echo.
echo [3/3] 测试数据库连接...
echo 激活conda环境并测试连接...

REM 激活conda环境
call conda activate fastapi-talking-pet-admin
if %errorlevel% neq 0 (
    echo WARNING: 无法激活conda环境，尝试直接运行Python测试
)

REM 运行数据库连接测试
python test_db_connection.py
if %errorlevel% neq 0 (
    echo ERROR: 数据库连接测试失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 数据库设置完成！
echo ========================================
echo 现在你可以：
echo 1. 运行 start_dev.bat 启动开发服务器
echo 2. 访问 http://localhost:51127/docs 查看API文档
echo ========================================
echo.
pause
