#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hello World API端点
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User

# 创建路由器
router = APIRouter(tags=["Hello World"])


@router.get("/")
async def root():
    """
    根路径，返回Hello World信息
    """
    return {
        "message": "Hello World!",
        "service": settings.app_name,
        "version": settings.app_version,
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs"
    }


@router.get("/hello")
async def hello_world():
    """
    Hello World端点
    """
    return {
        "message": "欢迎使用它宇宙后台管理系统!",
        "status": "success",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/health")
async def health_check():
    """
    健康检查端点
    """
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/db-test")
async def test_database(db: Session = Depends(get_db)):
    """
    测试数据库连接
    """
    try:
        # 尝试查询用户表
        user_count = db.query(User).count()
        return {
            "status": "database connected",
            "user_count": user_count,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "database error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
