# 它宇宙后台管理系统

这是一个基于 FastAPI 和 MySQL 的它宇宙后台管理系统，包含基本的项目结构和 API 接口。

## 项目结构

```
fastapi-talking-pet-admin/
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes.py          # 主路由器
│   │   └── endpoints/
│   │       ├── __init__.py
│   │       └── hello.py       # Hello World API端点
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py          # 应用配置
│   │   └── database.py        # 数据库配置
│   └── models/
│       ├── __init__.py
│       └── user.py            # 用户模型示例
├── main.py                    # 主应用文件
├── requirements.txt           # 依赖包
├── .env.example              # 环境变量示例
├── setup_conda_env.bat       # Windows conda环境自动设置脚本
├── start_dev.bat             # Windows开发模式启动脚本
└── README.md                 # 项目说明
```

## 快速开始

### Windows 用户（推荐）

#### 1. 自动环境设置

运行自动化脚本创建 conda 环境并安装依赖：

```batch
setup_conda_env.bat
```

这个脚本会：

- 创建名为 `fastapi-talking-pet-admin` 的 conda 环境（Python 3.10）
- 自动安装所有依赖
- 创建 `.env` 配置文件
- 提供启动选项

#### 2. 启动应用

**开发模式启动（推荐开发时使用）：**

```batch
start_dev.bat
```

**手动启动：**

```batch
# 生产模式
conda activate fastapi-talking-pet-admin
python main.py

# 开发模式
conda activate fastapi-talking-pet-admin
python -m uvicorn main:app --host 0.0.0.0 --port 51127 --reload
```

### 手动安装（Linux/macOS/Windows）

#### 1. 创建虚拟环境

使用 conda：

```bash
conda create -n fastapi-talking-pet-admin python=3.10 -y
conda activate fastapi-talking-pet-admin
```

或使用 venv：

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

#### 2. 安装依赖

```bash
pip install -r requirements.txt
```

#### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
# Windows
copy .env.example .env
# Linux/macOS
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接：

```
DATABASE_URL=mysql+pymysql://your_username:your_password@localhost:3306/your_database
```

#### 4. 启动应用

```bash
python main.py
```

或者使用 uvicorn：

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问 API

- API 文档: http://localhost:51127/docs（默认端口，可在.env 中修改）
- Hello World: http://localhost:51127/hello
- 健康检查: http://localhost:51127/health
- 数据库测试: http://localhost:51127/db-test

### 5. 开发模式说明

**开发模式（start_dev.bat）：**

- ✅ 自动热重载，代码修改后自动重启
- ✅ 详细的错误信息和调试输出
- ✅ 从.env 文件读取端口配置
- ✅ 适合开发和调试

**生产模式（手动启动）：**

- ✅ 使用 main.py 中的配置启动
- ✅ 更稳定的运行方式
- ✅ 适合生产环境部署

## API 端点

- `GET /` - 根路径，返回服务信息
- `GET /hello` - Hello World 端点
- `GET /health` - 健康检查
- `GET /db-test` - 测试数据库连接

## 依赖版本

基于原项目的依赖版本：

- FastAPI: 0.104.1
- Uvicorn: 0.24.0
- SQLAlchemy: 2.0.23
- PyMySQL: 1.1.0
- Pydantic: 2.5.0

## 数据库设置

确保 MySQL 服务正在运行，并创建对应的数据库：

```sql
CREATE DATABASE your_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

应用启动时会自动创建数据表。

## 脚本配置

### 自定义环境名称

如果您想使用不同的 conda 环境名称，请修改以下文件中的 `ENV_NAME` 变量：

**setup_conda_env.bat**:

```batch
set ENV_NAME=your-custom-env-name
```

**start_dev.bat**:

```batch
set ENV_NAME=your-custom-env-name
```

### 自定义 Python 版本

在 `setup_conda_env.bat` 中修改 `PYTHON_VERSION` 变量：

```batch
set PYTHON_VERSION=3.11
```

## 开发说明

这是它宇宙后台管理系统，您可以基于此系统扩展更多功能：

- 添加更多 API 端点
- 扩展数据模型
- 添加认证和授权
- 集成更多中间件
- 添加测试用例

## 许可证

MIT License
